import random
import requests
import json
import os

API_KEY = os.environ.get('LASTFM_API_KEY')

with open('usernames/members.txt', 'r') as f:
    members = f.read().splitlines()

username = random.choice(members)
print(username)

api_url = f"http://ws.audioscrobbler.com/2.0/?method=user.gettopalbums&user={username}&api_key={API_KEY}&format=json"

response = requests.get(api_url)
data = json.loads(response.text)

print(data)
top_albums = data['topalbums']['album']

for album in top_albums:
    print(album['name'])